<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Night & Day Animation</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Sky Background -->
        <div class="sky" id="sky">
            <!-- Stars for night scene -->
            <div class="stars" id="stars"></div>
            
            <!-- Moon -->
            <div class="moon" id="moon">
                <div class="moon-crater crater1"></div>
                <div class="moon-crater crater2"></div>
                <div class="moon-crater crater3"></div>
            </div>
            
            <!-- Sun -->
            <div class="sun" id="sun">
                <div class="sun-ray ray1"></div>
                <div class="sun-ray ray2"></div>
                <div class="sun-ray ray3"></div>
                <div class="sun-ray ray4"></div>
                <div class="sun-ray ray5"></div>
                <div class="sun-ray ray6"></div>
                <div class="sun-ray ray7"></div>
                <div class="sun-ray ray8"></div>
            </div>
            
            <!-- Clouds -->
            <div class="clouds">
                <div class="cloud cloud1"></div>
                <div class="cloud cloud2"></div>
                <div class="cloud cloud3"></div>
            </div>
        </div>
        
        <!-- Birds Canvas -->
        <canvas id="birdsCanvas" width="800" height="600"></canvas>
        
        <!-- Ground -->
        <div class="ground">
            <div class="grass"></div>
            <div class="tree">
                <div class="trunk"></div>
                <div class="leaves"></div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <button id="toggleBtn" class="toggle-btn">Switch to Day</button>
            <button id="soundBtn" class="sound-btn">🔊 Sound On</button>
        </div>
        
        <!-- Time Display -->
        <div class="time-display" id="timeDisplay">
            <span id="currentTime">Night Time</span>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
