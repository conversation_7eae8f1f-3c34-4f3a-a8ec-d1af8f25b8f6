* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    overflow: hidden;
    background: #000;
}

.container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Sky Background */
.sky {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 70%;
    background: linear-gradient(to bottom, #0c1445 0%, #1e3c72 50%, #2a5298 100%);
    transition: background 3s ease-in-out;
    overflow: hidden;
}

.sky.day {
    background: linear-gradient(to bottom, #87CEEB 0%, #98D8E8 30%, #F0E68C 70%, #FFD700 100%);
}

/* Stars */
.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 1;
    transition: opacity 2s ease-in-out;
}

.sky.day .stars {
    opacity: 0;
}

.star {
    position: absolute;
    background: white;
    border-radius: 50%;
    animation: twinkle 2s infinite alternate;
}

@keyframes twinkle {
    0% { opacity: 0.3; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.2); }
}

/* Moon */
.moon {
    position: absolute;
    width: 80px;
    height: 80px;
    background: #f5f5dc;
    border-radius: 50%;
    top: 15%;
    left: 20%;
    box-shadow: 0 0 20px rgba(245, 245, 220, 0.5);
    transition: all 3s ease-in-out;
    opacity: 1;
}

.sky.day .moon {
    opacity: 0;
    transform: translateY(-100px);
}

.moon-crater {
    position: absolute;
    background: #e6e6d4;
    border-radius: 50%;
}

.crater1 {
    width: 12px;
    height: 12px;
    top: 20px;
    left: 25px;
}

.crater2 {
    width: 8px;
    height: 8px;
    top: 35px;
    left: 45px;
}

.crater3 {
    width: 6px;
    height: 6px;
    top: 50px;
    left: 30px;
}

/* Sun */
.sun {
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, #FFD700 0%, #FFA500 100%);
    border-radius: 50%;
    top: 10%;
    right: 15%;
    opacity: 0;
    transform: translateY(-100px);
    transition: all 3s ease-in-out;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
}

.sky.day .sun {
    opacity: 1;
    transform: translateY(0);
    animation: sunGlow 4s ease-in-out infinite alternate;
}

@keyframes sunGlow {
    0% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
    100% { box-shadow: 0 0 50px rgba(255, 215, 0, 0.9); }
}

.sun-ray {
    position: absolute;
    background: #FFD700;
    border-radius: 2px;
    transform-origin: center;
    opacity: 0.7;
}

.ray1, .ray5 { width: 4px; height: 20px; top: -25px; left: 48px; }
.ray2, .ray6 { width: 4px; height: 20px; bottom: -25px; left: 48px; }
.ray3, .ray7 { width: 20px; height: 4px; top: 48px; left: -25px; }
.ray4, .ray8 { width: 20px; height: 4px; top: 48px; right: -25px; }

.ray1 { transform: rotate(45deg); }
.ray2 { transform: rotate(-45deg); }
.ray3 { transform: rotate(45deg); }
.ray4 { transform: rotate(-45deg); }

/* Clouds */
.clouds {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 2s ease-in-out;
}

.sky.day .clouds {
    opacity: 1;
}

.cloud {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
    animation: float 20s infinite linear;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
}

.cloud1 {
    width: 80px;
    height: 30px;
    top: 20%;
    left: -100px;
    animation-delay: 0s;
}

.cloud1::before {
    width: 40px;
    height: 40px;
    top: -15px;
    left: 10px;
}

.cloud1::after {
    width: 60px;
    height: 35px;
    top: -10px;
    right: 10px;
}

.cloud2 {
    width: 60px;
    height: 25px;
    top: 35%;
    left: -80px;
    animation-delay: -7s;
}

.cloud2::before {
    width: 30px;
    height: 30px;
    top: -10px;
    left: 15px;
}

.cloud2::after {
    width: 45px;
    height: 28px;
    top: -8px;
    right: 8px;
}

.cloud3 {
    width: 100px;
    height: 35px;
    top: 50%;
    left: -120px;
    animation-delay: -14s;
}

.cloud3::before {
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
}

.cloud3::after {
    width: 70px;
    height: 40px;
    top: -15px;
    right: 15px;
}

@keyframes float {
    0% { transform: translateX(0); }
    100% { transform: translateX(calc(100vw + 200px)); }
}

/* Birds Canvas */
#birdsCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

/* Ground */
.ground {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to bottom, #228B22 0%, #006400 100%);
    transition: background 3s ease-in-out;
}

.sky.day ~ .ground {
    background: linear-gradient(to bottom, #32CD32 0%, #228B22 100%);
}

.grass {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 20px;
    background: repeating-linear-gradient(
        90deg,
        #228B22 0px,
        #32CD32 2px,
        #228B22 4px
    );
}

/* Tree */
.tree {
    position: absolute;
    bottom: 20px;
    right: 15%;
}

.trunk {
    width: 20px;
    height: 80px;
    background: #8B4513;
    border-radius: 10px;
    margin: 0 auto;
}

.leaves {
    width: 80px;
    height: 80px;
    background: #228B22;
    border-radius: 50%;
    position: relative;
    top: -20px;
    left: -30px;
    transition: background 3s ease-in-out;
}

.sky.day ~ .ground .leaves {
    background: #32CD32;
}

/* Controls */
.controls {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 100;
}

.toggle-btn,
.sound-btn {
    padding: 10px 20px;
    margin: 5px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.toggle-btn:hover,
.sound-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Time Display */
.time-display {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: bold;
    z-index: 100;
}

/* Responsive Design */
@media (max-width: 768px) {
    .moon, .sun {
        width: 60px;
        height: 60px;
    }
    
    .controls {
        top: 10px;
        left: 10px;
    }
    
    .toggle-btn, .sound-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
    
    .time-display {
        top: 10px;
        right: 10px;
        padding: 8px 16px;
        font-size: 14px;
    }
}
