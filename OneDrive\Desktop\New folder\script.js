class NightDayAnimation {
    constructor() {
        this.isDay = false;
        this.soundEnabled = true;
        this.audioContext = null;
        this.birds = [];
        this.stars = [];
        
        this.initializeElements();
        this.createStars();
        this.initializeAudio();
        this.initializeBirds();
        this.setupEventListeners();
        this.startAnimation();
        this.startAutoTransition();
    }
    
    initializeElements() {
        this.sky = document.getElementById('sky');
        this.toggleBtn = document.getElementById('toggleBtn');
        this.soundBtn = document.getElementById('soundBtn');
        this.timeDisplay = document.getElementById('currentTime');
        this.canvas = document.getElementById('birdsCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Set canvas size
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }
    
    createStars() {
        const starsContainer = document.getElementById('stars');
        const numStars = 100;
        
        for (let i = 0; i < numStars; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            
            const size = Math.random() * 3 + 1;
            const x = Math.random() * 100;
            const y = Math.random() * 70;
            const delay = Math.random() * 2;
            
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;
            star.style.left = `${x}%`;
            star.style.top = `${y}%`;
            star.style.animationDelay = `${delay}s`;
            
            starsContainer.appendChild(star);
            this.stars.push(star);
        }
    }
    
    initializeAudio() {
        try {
            // Don't create AudioContext immediately - wait for user interaction
            this.audioContextCreated = false;
        } catch (e) {
            console.log('Web Audio API not supported');
            this.soundEnabled = false;
        }
    }

    createAudioContext() {
        if (!this.audioContextCreated && this.soundEnabled) {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                this.audioContextCreated = true;
                console.log('Audio context created successfully');
            } catch (e) {
                console.log('Failed to create audio context:', e);
                this.soundEnabled = false;
            }
        }
    }
    
    playSound(frequency, duration, type = 'sine', volume = 0.1) {
        if (!this.soundEnabled) return;

        // Create audio context if not created yet
        if (!this.audioContextCreated) {
            this.createAudioContext();
        }

        if (!this.audioContext) return;

        // Resume audio context if suspended
        if (this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                this.createAndPlayOscillator(frequency, duration, type, volume);
            });
        } else {
            this.createAndPlayOscillator(frequency, duration, type, volume);
        }
    }

    createAndPlayOscillator(frequency, duration, type, volume) {
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.1);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        } catch (e) {
            console.log('Error playing sound:', e);
        }
    }
    
    playBirdSound() {
        if (!this.soundEnabled) return;
        
        // Create bird chirping sound
        const frequencies = [800, 1000, 1200, 900];
        frequencies.forEach((freq, index) => {
            setTimeout(() => {
                this.playSound(freq, 0.2, 'sine', 0.05);
            }, index * 100);
        });
    }
    
    playTransitionSound() {
        if (!this.soundEnabled) return;
        
        // Create magical transition sound
        for (let i = 0; i < 10; i++) {
            setTimeout(() => {
                const freq = 200 + (i * 50);
                this.playSound(freq, 0.3, 'triangle', 0.03);
            }, i * 50);
        }
    }
    
    initializeBirds() {
        const numBirds = 8;
        for (let i = 0; i < numBirds; i++) {
            this.birds.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * (this.canvas.height * 0.6) + 50,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 1,
                wingPhase: Math.random() * Math.PI * 2,
                size: Math.random() * 0.5 + 0.5,
                visible: false
            });
        }
    }
    
    drawBird(bird) {
        if (!bird.visible) return;
        
        this.ctx.save();
        this.ctx.translate(bird.x, bird.y);
        this.ctx.scale(bird.size, bird.size);
        
        // Bird body
        this.ctx.fillStyle = '#333';
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, 8, 4, 0, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Wings
        const wingOffset = Math.sin(bird.wingPhase) * 10;
        this.ctx.strokeStyle = '#333';
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round';
        
        // Left wing
        this.ctx.beginPath();
        this.ctx.moveTo(-5, -2);
        this.ctx.lineTo(-15, -8 + wingOffset);
        this.ctx.stroke();
        
        // Right wing
        this.ctx.beginPath();
        this.ctx.moveTo(5, -2);
        this.ctx.lineTo(15, -8 + wingOffset);
        this.ctx.stroke();
        
        this.ctx.restore();
    }
    
    updateBirds() {
        this.birds.forEach(bird => {
            if (!bird.visible) return;
            
            bird.x += bird.vx;
            bird.y += bird.vy;
            bird.wingPhase += 0.3;
            
            // Boundary checking
            if (bird.x < -50) bird.x = this.canvas.width + 50;
            if (bird.x > this.canvas.width + 50) bird.x = -50;
            if (bird.y < 30) bird.vy = Math.abs(bird.vy);
            if (bird.y > this.canvas.height * 0.6) bird.vy = -Math.abs(bird.vy);
            
            // Random direction changes
            if (Math.random() < 0.01) {
                bird.vx += (Math.random() - 0.5) * 0.5;
                bird.vy += (Math.random() - 0.5) * 0.3;
                
                // Limit speed
                const speed = Math.sqrt(bird.vx * bird.vx + bird.vy * bird.vy);
                if (speed > 3) {
                    bird.vx = (bird.vx / speed) * 3;
                    bird.vy = (bird.vy / speed) * 3;
                }
            }
        });
    }
    
    animateBirds() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (this.isDay) {
            this.updateBirds();
            this.birds.forEach(bird => this.drawBird(bird));
        }
        
        requestAnimationFrame(() => this.animateBirds());
    }
    
    toggleDayNight() {
        this.isDay = !this.isDay;
        
        if (this.isDay) {
            this.sky.classList.add('day');
            this.toggleBtn.textContent = 'Switch to Night';
            this.timeDisplay.textContent = 'Day Time';
            
            // Show birds
            this.birds.forEach(bird => {
                bird.visible = true;
            });
            
            // Play bird sounds
            setTimeout(() => this.playBirdSound(), 1000);
            setTimeout(() => this.playBirdSound(), 3000);
            setTimeout(() => this.playBirdSound(), 5000);
            
        } else {
            this.sky.classList.remove('day');
            this.toggleBtn.textContent = 'Switch to Day';
            this.timeDisplay.textContent = 'Night Time';
            
            // Hide birds
            this.birds.forEach(bird => {
                bird.visible = false;
            });
        }
        
        this.playTransitionSound();
    }
    
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        this.soundBtn.textContent = this.soundEnabled ? '🔊 Sound On' : '🔇 Sound Off';
        
        if (this.soundEnabled && this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }
    
    setupEventListeners() {
        this.toggleBtn.addEventListener('click', () => {
            this.createAudioContext(); // Ensure audio context is created on user interaction
            this.toggleDayNight();
        });

        this.soundBtn.addEventListener('click', () => {
            this.createAudioContext(); // Ensure audio context is created on user interaction
            this.toggleSound();
        });

        // Initialize audio context on any user interaction
        const initAudio = () => {
            this.createAudioContext();
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
        };

        document.addEventListener('click', initAudio, { once: true });
        document.addEventListener('keydown', initAudio, { once: true });
        document.addEventListener('touchstart', initAudio, { once: true });
    }
    
    startAnimation() {
        this.animateBirds();
    }
    
    startAutoTransition() {
        // Auto transition every 15 seconds
        setInterval(() => {
            this.toggleDayNight();
        }, 15000);
    }
}

// Initialize the animation when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new NightDayAnimation();
});
